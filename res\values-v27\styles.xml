<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="PYPLAppFullScreenTheme" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="PYPLAppTheme" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="bottomSheetDialogTheme">@style/PYPLBottomSheetDialogTheme</item>
    </style>
    <style name="Theme_ABox_Splash" parent="@style/Theme_ABox">
        <item name="android:windowBackground">@drawable/splash_window</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
