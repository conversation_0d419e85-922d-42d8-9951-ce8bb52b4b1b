<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V26.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V26.Theme.AppCompat.Light" />
    <style name="Base.Widget.AppCompat.Toolbar" parent="@style/Base.V26.Widget.AppCompat.Toolbar" />
    <style name="PYPLAppFullScreenTheme" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="PYPLAppTheme" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="bottomSheetDialogTheme">@style/PYPLBottomSheetDialogTheme</item>
    </style>
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?colorPrimary</item>
        <item name="android:stateListAnimator">@animator/design_appbar_state_list_animator</item>
        <item name="android:touchscreenBlocksFocus">true</item>
        <item name="android:keyboardNavigationCluster">true</item>
    </style>
    <style name="Base.V26.Theme.AppCompat" parent="@style/Base.V23.Theme.AppCompat">
        <item name="colorError">?android:colorError</item>
    </style>
    <style name="Base.V26.Theme.AppCompat.Light" parent="@style/Base.V23.Theme.AppCompat.Light">
        <item name="colorError">?android:colorError</item>
    </style>
    <style name="Base.V26.Widget.AppCompat.Toolbar" parent="@style/Base.V7.Widget.AppCompat.Toolbar">
        <item name="android:touchscreenBlocksFocus">true</item>
        <item name="android:keyboardNavigationCluster">true</item>
    </style>
</resources>
