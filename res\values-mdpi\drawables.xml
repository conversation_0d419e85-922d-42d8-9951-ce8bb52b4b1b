<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="drawable" name="applovin_exo_edit_mode_logo">ts</item>
    <item type="drawable" name="applovin_exo_ic_audiotrack">aac</item>
    <item type="drawable" name="applovin_exo_ic_check">aay</item>
    <item type="drawable" name="applovin_exo_ic_chevron_left">nl</item>
    <item type="drawable" name="applovin_exo_ic_chevron_right">qp</item>
    <item type="drawable" name="applovin_exo_ic_default_album_image">aoj</item>
    <item type="drawable" name="applovin_exo_ic_forward">a</item>
    <item type="drawable" name="applovin_exo_ic_fullscreen_enter">agt</item>
    <item type="drawable" name="applovin_exo_ic_fullscreen_exit">qz</item>
    <item type="drawable" name="applovin_exo_ic_pause_circle_filled">jr</item>
    <item type="drawable" name="applovin_exo_ic_play_circle_filled">qt</item>
    <item type="drawable" name="applovin_exo_ic_rewind">jm</item>
    <item type="drawable" name="applovin_exo_ic_settings">aql</item>
    <item type="drawable" name="applovin_exo_ic_skip_next">bd</item>
    <item type="drawable" name="applovin_exo_ic_skip_previous">tc</item>
    <item type="drawable" name="applovin_exo_ic_speed">ans</item>
    <item type="drawable" name="applovin_exo_ic_subtitle_off">tq</item>
    <item type="drawable" name="applovin_exo_ic_subtitle_on">aqd</item>
    <item type="drawable" name="applovin_exo_icon_fastforward">cn</item>
    <item type="drawable" name="applovin_exo_icon_fullscreen_enter">ew</item>
    <item type="drawable" name="applovin_exo_icon_fullscreen_exit">rf</item>
    <item type="drawable" name="applovin_exo_icon_next">ce</item>
    <item type="drawable" name="applovin_exo_icon_pause">aoe</item>
    <item type="drawable" name="applovin_exo_icon_play">aeu</item>
    <item type="drawable" name="applovin_exo_icon_previous">ty</item>
    <item type="drawable" name="applovin_exo_icon_repeat_all">om</item>
    <item type="drawable" name="applovin_exo_icon_repeat_off">lm</item>
    <item type="drawable" name="applovin_exo_icon_repeat_one">ang</item>
    <item type="drawable" name="applovin_exo_icon_rewind">qb</item>
    <item type="drawable" name="applovin_exo_icon_shuffle_off">arc</item>
    <item type="drawable" name="applovin_exo_icon_shuffle_on">akw</item>
    <item type="drawable" name="applovin_exo_icon_stop">wi</item>
    <item type="drawable" name="applovin_ic_baseline_add_circle_outline">uo</item>
    <item type="drawable" name="applovin_ic_check_mark_bordered">ob</item>
    <item type="drawable" name="applovin_ic_check_mark_borderless">amk</item>
    <item type="drawable" name="applovin_ic_disclosure_arrow">o</item>
    <item type="drawable" name="applovin_ic_mediation_adcolony">ib</item>
    <item type="drawable" name="applovin_ic_mediation_admob">zw</item>
    <item type="drawable" name="applovin_ic_mediation_amazon_marketplace">h</item>
    <item type="drawable" name="applovin_ic_mediation_applovin">aas</item>
    <item type="drawable" name="applovin_ic_mediation_bidmachine">ahl</item>
    <item type="drawable" name="applovin_ic_mediation_chartboost">px</item>
    <item type="drawable" name="applovin_ic_mediation_criteo">ud</item>
    <item type="drawable" name="applovin_ic_mediation_facebook">sk</item>
    <item type="drawable" name="applovin_ic_mediation_fyber">fq</item>
    <item type="drawable" name="applovin_ic_mediation_google_ad_manager">kv</item>
    <item type="drawable" name="applovin_ic_mediation_hyprmx">ix</item>
    <item type="drawable" name="applovin_ic_mediation_inmobi">amv</item>
    <item type="drawable" name="applovin_ic_mediation_ironsource">bz</item>
    <item type="drawable" name="applovin_ic_mediation_line">mu</item>
    <item type="drawable" name="applovin_ic_mediation_maio">tk</item>
    <item type="drawable" name="applovin_ic_mediation_mintegral">adj</item>
    <item type="drawable" name="applovin_ic_mediation_mobilefuse">mx</item>
    <item type="drawable" name="applovin_ic_mediation_mytarget">mp</item>
    <item type="drawable" name="applovin_ic_mediation_nend">asg</item>
    <item type="drawable" name="applovin_ic_mediation_ogury_presage">jt</item>
    <item type="drawable" name="applovin_ic_mediation_pangle">aia</item>
    <item type="drawable" name="applovin_ic_mediation_smaato">or</item>
    <item type="drawable" name="applovin_ic_mediation_tapjoy">aqc</item>
    <item type="drawable" name="applovin_ic_mediation_tiktok">un</item>
    <item type="drawable" name="applovin_ic_mediation_unity">aew</item>
    <item type="drawable" name="applovin_ic_mediation_verve">dv</item>
    <item type="drawable" name="applovin_ic_mediation_vungle">nz</item>
    <item type="drawable" name="applovin_ic_mediation_yandex">ny</item>
    <item type="drawable" name="applovin_ic_pause_icon">ard</item>
    <item type="drawable" name="applovin_ic_play_icon">rx</item>
    <item type="drawable" name="applovin_ic_replay_icon">vr</item>
    <item type="drawable" name="applovin_ic_warning">add</item>
    <item type="drawable" name="applovin_ic_warning_outline">ta</item>
    <item type="drawable" name="applovin_ic_x_mark">afj</item>
    <item type="drawable" name="fingerprint_dialog_error">s</item>
    <item type="drawable" name="ic_arrow_back_icon">agn</item>
    <item type="drawable" name="ic_check_white_24dp">zz</item>
    <item type="drawable" name="ic_clear_white_24dp">amn</item>
    <item type="drawable" name="ic_error_outline_white_24dp">akl</item>
    <item type="drawable" name="ic_header_back">apt</item>
    <item type="drawable" name="ic_header_paypal_mark_color">wd</item>
    <item type="drawable" name="ic_info_outline_white_24dp">va</item>
    <item type="drawable" name="ic_paypal_color">acr</item>
    <item type="drawable" name="ic_paypal_mark_color">oe</item>
    <item type="drawable" name="toast_frame">ky</item>
    <item type="drawable" name="abc_ab_share_pack_mtrl_alpha">afl</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_000">aek</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_015">vi</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_000">ald</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_015">wx</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00001">alo</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00012">zv</item>
    <item type="drawable" name="abc_cab_background_top_mtrl_alpha">zd</item>
    <item type="drawable" name="abc_ic_commit_search_api_mtrl_alpha">iu</item>
    <item type="drawable" name="abc_list_divider_mtrl_alpha">aep</item>
    <item type="drawable" name="abc_list_focused_holo">ke</item>
    <item type="drawable" name="abc_list_longpressed_holo">ss</item>
    <item type="drawable" name="abc_list_pressed_holo_dark">bm</item>
    <item type="drawable" name="abc_list_pressed_holo_light">qm</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_dark">aoi</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_light">afa</item>
    <item type="drawable" name="abc_menu_hardkey_panel_mtrl_mult">mm</item>
    <item type="drawable" name="abc_popup_background_mtrl_mult">acg</item>
    <item type="drawable" name="abc_scrubber_control_off_mtrl_alpha">aw</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000">aoc</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005">cf</item>
    <item type="drawable" name="abc_scrubber_primary_mtrl_alpha">nn</item>
    <item type="drawable" name="abc_scrubber_track_mtrl_alpha">ahu</item>
    <item type="drawable" name="abc_spinner_mtrl_am_alpha">vw</item>
    <item type="drawable" name="abc_switch_track_mtrl_alpha">ami</item>
    <item type="drawable" name="abc_tab_indicator_mtrl_alpha">if</item>
    <item type="drawable" name="abc_text_select_handle_left_mtrl">lx</item>
    <item type="drawable" name="abc_text_select_handle_middle_mtrl">eg</item>
    <item type="drawable" name="abc_text_select_handle_right_mtrl">dg</item>
    <item type="drawable" name="abc_textfield_activated_mtrl_alpha">dq</item>
    <item type="drawable" name="abc_textfield_default_mtrl_alpha">ahh</item>
    <item type="drawable" name="abc_textfield_search_activated_mtrl_alpha">zk</item>
    <item type="drawable" name="abc_textfield_search_default_mtrl_alpha">aiv</item>
    <item type="drawable" name="amp_banner">rq</item>
    <item type="drawable" name="amp_cancel">arg</item>
    <item type="drawable" name="amp_logo">aja</item>
    <item type="drawable" name="com_facebook_button_like_icon_selected">ajy</item>
    <item type="drawable" name="com_facebook_close">kk</item>
    <item type="drawable" name="com_facebook_profile_picture_blank_portrait">amd</item>
    <item type="drawable" name="com_facebook_profile_picture_blank_square">abs</item>
    <item type="drawable" name="com_facebook_tooltip_black_background">aab</item>
    <item type="drawable" name="com_facebook_tooltip_black_bottomnub">aji</item>
    <item type="drawable" name="com_facebook_tooltip_black_topnub">jb</item>
    <item type="drawable" name="com_facebook_tooltip_black_xout">ail</item>
    <item type="drawable" name="com_facebook_tooltip_blue_background">yr</item>
    <item type="drawable" name="com_facebook_tooltip_blue_bottomnub">aoq</item>
    <item type="drawable" name="com_facebook_tooltip_blue_topnub">alz</item>
    <item type="drawable" name="com_facebook_tooltip_blue_xout">hq</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark_normal_background">alj</item>
    <item type="drawable" name="common_google_signin_btn_icon_light_normal_background">ms</item>
    <item type="drawable" name="common_google_signin_btn_text_dark_normal_background">aog</item>
    <item type="drawable" name="common_google_signin_btn_text_light_normal_background">aqb</item>
    <item type="drawable" name="googleg_disabled_color_18">qv</item>
    <item type="drawable" name="googleg_standard_color_18">aqs</item>
    <item type="drawable" name="notification_bg_low_normal">so</item>
    <item type="drawable" name="notification_bg_low_pressed">aef</item>
    <item type="drawable" name="notification_bg_normal">agv</item>
    <item type="drawable" name="notification_bg_normal_pressed">rg</item>
    <item type="drawable" name="notify_panel_notification_icon_bg">tm</item>
</resources>
