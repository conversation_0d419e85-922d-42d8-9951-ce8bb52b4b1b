{"v": "5.8.1", "fr": 30, "ip": 16, "op": 37, "w": 128, "h": 128, "nm": "A-voice", "ddd": 0, "assets": [{"id": "comp_0", "nm": "C-跳动 合成 1", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "C-跳动", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [4, 28, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.062, -4.375, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 0, "s": [8, 52]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 1, "s": [8, 48.8]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 2, "s": [8, 45.6]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 3, "s": [8, 42.4]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 4, "s": [8, 39.2]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 5, "s": [8, 36]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 6, "s": [8, 32.8]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 7, "s": [8, 29.6]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 8, "s": [8, 26.4]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.944]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 9, "s": [8, 23.2]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.759]}, "o": {"x": [0.167, 0.167], "y": [0, -0.175]}, "t": 10, "s": [8, 20]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.802]}, "o": {"x": [0.167, 0.167], "y": [0, 0.127]}, "t": 11, "s": [8, 21.034]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.817]}, "o": {"x": [0.167, 0.167], "y": [0, 0.144]}, "t": 12, "s": [8, 22.99]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.824]}, "o": {"x": [0.167, 0.167], "y": [0, 0.153]}, "t": 13, "s": [8, 25.685]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.829]}, "o": {"x": [0.167, 0.167], "y": [0, 0.158]}, "t": 14, "s": [8, 28.923]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.832]}, "o": {"x": [0.167, 0.167], "y": [0, 0.162]}, "t": 15, "s": [8, 32.522]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.834]}, "o": {"x": [0.167, 0.167], "y": [0, 0.165]}, "t": 16, "s": [8, 36.324]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.835]}, "o": {"x": [0.167, 0.167], "y": [0, 0.167]}, "t": 17, "s": [8, 40.205]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.826]}, "o": {"x": [0.167, 0.167], "y": [0, 0.168]}, "t": 18, "s": [8, 44.073]}, {"i": {"x": [0.833, 0.833], "y": [1, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0, 0.16]}, "t": 19, "s": [8, 47.867]}, {"t": 20, "s": [8, 52]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 492, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.062, -4.375], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 21, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 5, "ty": 0, "nm": "C-跳动 合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [32, 64, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [4, 28, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [0]}, {"t": 36, "s": [0.667], "h": 1}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOutDuration('cycle', 0);"}, "w": 8, "h": 56, "ip": 16, "op": 137, "st": 16, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "C-跳动 合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [48, 64, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [4, 28, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [0]}, {"t": 32, "s": [0.667], "h": 1}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOutDuration('cycle', 0);"}, "w": 8, "h": 56, "ip": 12, "op": 133, "st": 12, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "C-跳动 合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 64, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [4, 28, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [0]}, {"t": 28, "s": [0.667], "h": 1}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOutDuration('cycle', 0);"}, "w": 8, "h": 56, "ip": 8, "op": 129, "st": 8, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "C-跳动 合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [80, 64, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [4, 28, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0]}, {"t": 24, "s": [0.667], "h": 1}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOutDuration('cycle', 0);"}, "w": 8, "h": 56, "ip": 4, "op": 125, "st": 4, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "C-跳动 合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [96, 64, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [4, 28, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 20, "s": [0.667], "h": 1}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOutDuration('cycle', 0);"}, "w": 8, "h": 56, "ip": 0, "op": 121, "st": 0, "bm": 0}], "markers": []}