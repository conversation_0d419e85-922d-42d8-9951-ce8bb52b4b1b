.class public interface abstract Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "VisualMediaType"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001\u0082\u0001\u0004\u0002\u0003\u0004\u0005\u00f8\u0001\u0000\u0082\u0002\u0006\n\u0004\u0008!0\u0001\u00a8\u0006\u0006\u00c0\u0006\u0001"
    }
    d2 = {
        "Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType;",
        "",
        "Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo;",
        "Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly;",
        "Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType;",
        "Landroidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly;",
        "activity_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation
