<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="drawable" name="applovin_exo_edit_mode_logo">ame</item>
    <item type="drawable" name="applovin_exo_ic_audiotrack">yd</item>
    <item type="drawable" name="applovin_exo_ic_check">abw</item>
    <item type="drawable" name="applovin_exo_ic_chevron_left">fe</item>
    <item type="drawable" name="applovin_exo_ic_chevron_right">zu</item>
    <item type="drawable" name="applovin_exo_ic_default_album_image">me</item>
    <item type="drawable" name="applovin_exo_ic_forward">sj</item>
    <item type="drawable" name="applovin_exo_ic_fullscreen_enter">agq</item>
    <item type="drawable" name="applovin_exo_ic_fullscreen_exit">ajg</item>
    <item type="drawable" name="applovin_exo_ic_pause_circle_filled">aot</item>
    <item type="drawable" name="applovin_exo_ic_play_circle_filled">arj</item>
    <item type="drawable" name="applovin_exo_ic_rewind">gv</item>
    <item type="drawable" name="applovin_exo_ic_settings">au</item>
    <item type="drawable" name="applovin_exo_ic_skip_next">kr</item>
    <item type="drawable" name="applovin_exo_ic_skip_previous">anf</item>
    <item type="drawable" name="applovin_exo_ic_speed">tx</item>
    <item type="drawable" name="applovin_exo_ic_subtitle_off">aee</item>
    <item type="drawable" name="applovin_exo_ic_subtitle_on">alq</item>
    <item type="drawable" name="applovin_exo_icon_fastforward">atd</item>
    <item type="drawable" name="applovin_exo_icon_fullscreen_enter">yz</item>
    <item type="drawable" name="applovin_exo_icon_fullscreen_exit">alm</item>
    <item type="drawable" name="applovin_exo_icon_next">aht</item>
    <item type="drawable" name="applovin_exo_icon_pause">aq</item>
    <item type="drawable" name="applovin_exo_icon_play">oj</item>
    <item type="drawable" name="applovin_exo_icon_previous">xg</item>
    <item type="drawable" name="applovin_exo_icon_repeat_all">hp</item>
    <item type="drawable" name="applovin_exo_icon_repeat_off">aka</item>
    <item type="drawable" name="applovin_exo_icon_repeat_one">vq</item>
    <item type="drawable" name="applovin_exo_icon_rewind">xr</item>
    <item type="drawable" name="applovin_exo_icon_shuffle_off">agu</item>
    <item type="drawable" name="applovin_exo_icon_shuffle_on">rh</item>
    <item type="drawable" name="applovin_exo_icon_stop">adu</item>
    <item type="drawable" name="applovin_ic_baseline_add_circle_outline">abh</item>
    <item type="drawable" name="applovin_ic_check_mark_bordered">arq</item>
    <item type="drawable" name="applovin_ic_check_mark_borderless">aco</item>
    <item type="drawable" name="applovin_ic_disclosure_arrow">aky</item>
    <item type="drawable" name="applovin_ic_mediation_adcolony">iy</item>
    <item type="drawable" name="applovin_ic_mediation_admob">amf</item>
    <item type="drawable" name="applovin_ic_mediation_amazon_marketplace">asy</item>
    <item type="drawable" name="applovin_ic_mediation_applovin">ati</item>
    <item type="drawable" name="applovin_ic_mediation_bidmachine">gk</item>
    <item type="drawable" name="applovin_ic_mediation_chartboost">amt</item>
    <item type="drawable" name="applovin_ic_mediation_criteo">mo</item>
    <item type="drawable" name="applovin_ic_mediation_facebook">wy</item>
    <item type="drawable" name="applovin_ic_mediation_fyber">iv</item>
    <item type="drawable" name="applovin_ic_mediation_google_ad_manager">anr</item>
    <item type="drawable" name="applovin_ic_mediation_hyprmx">ek</item>
    <item type="drawable" name="applovin_ic_mediation_inmobi">iz</item>
    <item type="drawable" name="applovin_ic_mediation_ironsource">aft</item>
    <item type="drawable" name="applovin_ic_mediation_line">kc</item>
    <item type="drawable" name="applovin_ic_mediation_maio">ago</item>
    <item type="drawable" name="applovin_ic_mediation_mintegral">uv</item>
    <item type="drawable" name="applovin_ic_mediation_mobilefuse">pr</item>
    <item type="drawable" name="applovin_ic_mediation_mytarget">vz</item>
    <item type="drawable" name="applovin_ic_mediation_nend">br</item>
    <item type="drawable" name="applovin_ic_mediation_ogury_presage">aqy</item>
    <item type="drawable" name="applovin_ic_mediation_pangle">uj</item>
    <item type="drawable" name="applovin_ic_mediation_smaato">gc</item>
    <item type="drawable" name="applovin_ic_mediation_tapjoy">aha</item>
    <item type="drawable" name="applovin_ic_mediation_tiktok">fm</item>
    <item type="drawable" name="applovin_ic_mediation_unity">uk</item>
    <item type="drawable" name="applovin_ic_mediation_verve">apw</item>
    <item type="drawable" name="applovin_ic_mediation_vungle">ua</item>
    <item type="drawable" name="applovin_ic_mediation_yandex">aap</item>
    <item type="drawable" name="applovin_ic_pause_icon">aej</item>
    <item type="drawable" name="applovin_ic_play_icon">xb</item>
    <item type="drawable" name="applovin_ic_replay_icon">aga</item>
    <item type="drawable" name="applovin_ic_warning">ajh</item>
    <item type="drawable" name="applovin_ic_warning_outline">arw</item>
    <item type="drawable" name="applovin_ic_x_mark">ww</item>
    <item type="drawable" name="fingerprint_dialog_error">akt</item>
    <item type="drawable" name="ic_arrow_back_icon">aqg</item>
    <item type="drawable" name="ic_check_white_24dp">acl</item>
    <item type="drawable" name="ic_clear_white_24dp">ai</item>
    <item type="drawable" name="ic_error_outline_white_24dp">ahs</item>
    <item type="drawable" name="ic_header_back">cx</item>
    <item type="drawable" name="ic_header_paypal_mark_color">adp</item>
    <item type="drawable" name="ic_info_outline_white_24dp">afw</item>
    <item type="drawable" name="ic_paypal_color">fz</item>
    <item type="drawable" name="ic_paypal_mark_color">db</item>
    <item type="drawable" name="toast_frame">abe</item>
    <item type="drawable" name="abc_ab_share_pack_mtrl_alpha">rp</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_000">dn</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_015">ad</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_000">vb</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_015">aaz</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00001">lg</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00012">aru</item>
    <item type="drawable" name="abc_cab_background_top_mtrl_alpha">arb</item>
    <item type="drawable" name="abc_ic_commit_search_api_mtrl_alpha">pe</item>
    <item type="drawable" name="abc_list_divider_mtrl_alpha">lv</item>
    <item type="drawable" name="abc_list_focused_holo">aax</item>
    <item type="drawable" name="abc_list_longpressed_holo">apf</item>
    <item type="drawable" name="abc_list_pressed_holo_dark">adk</item>
    <item type="drawable" name="abc_list_pressed_holo_light">ws</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_dark">qo</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_light">fk</item>
    <item type="drawable" name="abc_menu_hardkey_panel_mtrl_mult">kz</item>
    <item type="drawable" name="abc_popup_background_mtrl_mult">agb</item>
    <item type="drawable" name="abc_scrubber_control_off_mtrl_alpha">ne</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000">akm</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005">ga</item>
    <item type="drawable" name="abc_scrubber_primary_mtrl_alpha">ajz</item>
    <item type="drawable" name="abc_scrubber_track_mtrl_alpha">aea</item>
    <item type="drawable" name="abc_spinner_mtrl_am_alpha">arz</item>
    <item type="drawable" name="abc_switch_track_mtrl_alpha">fn</item>
    <item type="drawable" name="abc_tab_indicator_mtrl_alpha">agk</item>
    <item type="drawable" name="abc_text_select_handle_left_mtrl">nw</item>
    <item type="drawable" name="abc_text_select_handle_middle_mtrl">alk</item>
    <item type="drawable" name="abc_text_select_handle_right_mtrl">kt</item>
    <item type="drawable" name="abc_textfield_activated_mtrl_alpha">fb</item>
    <item type="drawable" name="abc_textfield_default_mtrl_alpha">arl</item>
    <item type="drawable" name="abc_textfield_search_activated_mtrl_alpha">ks</item>
    <item type="drawable" name="abc_textfield_search_default_mtrl_alpha">jj</item>
    <item type="drawable" name="com_facebook_button_like_icon_selected">ahk</item>
    <item type="drawable" name="com_facebook_close">mr</item>
    <item type="drawable" name="com_facebook_tooltip_black_background">ze</item>
    <item type="drawable" name="com_facebook_tooltip_black_bottomnub">aqp</item>
    <item type="drawable" name="com_facebook_tooltip_black_topnub">gh</item>
    <item type="drawable" name="com_facebook_tooltip_black_xout">ho</item>
    <item type="drawable" name="com_facebook_tooltip_blue_background">acs</item>
    <item type="drawable" name="com_facebook_tooltip_blue_bottomnub">ama</item>
    <item type="drawable" name="com_facebook_tooltip_blue_topnub">apz</item>
    <item type="drawable" name="com_facebook_tooltip_blue_xout">aly</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark_normal_background">anu</item>
    <item type="drawable" name="common_google_signin_btn_icon_light_normal_background">sh</item>
    <item type="drawable" name="common_google_signin_btn_text_dark_normal_background">aou</item>
    <item type="drawable" name="common_google_signin_btn_text_light_normal_background">ata</item>
    <item type="drawable" name="googleg_disabled_color_18">ik</item>
    <item type="drawable" name="googleg_standard_color_18">to</item>
    <item type="drawable" name="notification_bg_low_normal">z</item>
    <item type="drawable" name="notification_bg_low_pressed">uy</item>
    <item type="drawable" name="notification_bg_normal">ahz</item>
    <item type="drawable" name="notification_bg_normal_pressed">yu</item>
    <item type="drawable" name="notify_panel_notification_icon_bg">pc</item>
    <item type="drawable" name="common_full_open_on_phone">nr</item>
</resources>
