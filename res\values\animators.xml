<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="animator" name="anim_blink">ph.xml</item>
    <item type="animator" name="design_appbar_state_list_animator">il.xml</item>
    <item type="animator" name="design_fab_hide_motion_spec">qb.xml</item>
    <item type="animator" name="design_fab_show_motion_spec">amo.xml</item>
    <item type="animator" name="fragment_close_enter">aiw.xml</item>
    <item type="animator" name="fragment_close_exit">abg.xml</item>
    <item type="animator" name="fragment_fade_enter">sa.xml</item>
    <item type="animator" name="fragment_fade_exit">aji.xml</item>
    <item type="animator" name="fragment_open_enter">ani.xml</item>
    <item type="animator" name="fragment_open_exit">mx.xml</item>
    <item type="animator" name="linear_indeterminate_line1_head_interpolator">bd.xml</item>
    <item type="animator" name="linear_indeterminate_line1_tail_interpolator">me.xml</item>
    <item type="animator" name="linear_indeterminate_line2_head_interpolator">mi.xml</item>
    <item type="animator" name="linear_indeterminate_line2_tail_interpolator">ir.xml</item>
    <item type="animator" name="m3_btn_elevated_btn_state_list_anim">rn.xml</item>
    <item type="animator" name="m3_btn_state_list_anim">kx.xml</item>
    <item type="animator" name="m3_card_elevated_state_list_anim">mv.xml</item>
    <item type="animator" name="m3_card_state_list_anim">go.xml</item>
    <item type="animator" name="m3_chip_state_list_anim">ake.xml</item>
    <item type="animator" name="m3_elevated_chip_state_list_anim">tf.xml</item>
    <item type="animator" name="mtrl_btn_state_list_anim">ain.xml</item>
    <item type="animator" name="mtrl_btn_unelevated_state_list_anim">agt.xml</item>
    <item type="animator" name="mtrl_card_state_list_anim">ajm.xml</item>
    <item type="animator" name="mtrl_chip_state_list_anim">ahv.xml</item>
    <item type="animator" name="mtrl_extended_fab_change_size_collapse_motion_spec">cu.xml</item>
    <item type="animator" name="mtrl_extended_fab_change_size_expand_motion_spec">sy.xml</item>
    <item type="animator" name="mtrl_extended_fab_hide_motion_spec">akk.xml</item>
    <item type="animator" name="mtrl_extended_fab_show_motion_spec">pz.xml</item>
    <item type="animator" name="mtrl_extended_fab_state_list_animator">it.xml</item>
    <item type="animator" name="mtrl_fab_hide_motion_spec">and.xml</item>
    <item type="animator" name="mtrl_fab_show_motion_spec">ahe.xml</item>
    <item type="animator" name="mtrl_fab_transformation_sheet_collapse_spec">tg.xml</item>
    <item type="animator" name="mtrl_fab_transformation_sheet_expand_spec">anh.xml</item>
    <item type="animator" name="nav_default_enter_anim">ajx.xml</item>
    <item type="animator" name="nav_default_exit_anim">ha.xml</item>
    <item type="animator" name="nav_default_pop_enter_anim">tq.xml</item>
    <item type="animator" name="nav_default_pop_exit_anim">agy.xml</item>
</resources>
