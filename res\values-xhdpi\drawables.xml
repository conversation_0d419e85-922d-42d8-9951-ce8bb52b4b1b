<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="drawable" name="applovin_exo_edit_mode_logo">ox</item>
    <item type="drawable" name="applovin_exo_ic_audiotrack">aik</item>
    <item type="drawable" name="applovin_exo_ic_check">vv</item>
    <item type="drawable" name="applovin_exo_ic_chevron_left">pm</item>
    <item type="drawable" name="applovin_exo_ic_chevron_right">dx</item>
    <item type="drawable" name="applovin_exo_ic_default_album_image">atg</item>
    <item type="drawable" name="applovin_exo_ic_forward">ajw</item>
    <item type="drawable" name="applovin_exo_ic_fullscreen_enter">apq</item>
    <item type="drawable" name="applovin_exo_ic_fullscreen_exit">amx</item>
    <item type="drawable" name="applovin_exo_ic_pause_circle_filled">as</item>
    <item type="drawable" name="applovin_exo_ic_play_circle_filled">gu</item>
    <item type="drawable" name="applovin_exo_ic_rewind">kn</item>
    <item type="drawable" name="applovin_exo_ic_settings">acp</item>
    <item type="drawable" name="applovin_exo_ic_skip_next">aae</item>
    <item type="drawable" name="applovin_exo_ic_skip_previous">ajl</item>
    <item type="drawable" name="applovin_exo_ic_speed">td</item>
    <item type="drawable" name="applovin_exo_ic_subtitle_off">pi</item>
    <item type="drawable" name="applovin_exo_ic_subtitle_on">aaq</item>
    <item type="drawable" name="applovin_exo_icon_fastforward">ht</item>
    <item type="drawable" name="applovin_exo_icon_fullscreen_enter">apy</item>
    <item type="drawable" name="applovin_exo_icon_fullscreen_exit">hd</item>
    <item type="drawable" name="applovin_exo_icon_next">afr</item>
    <item type="drawable" name="applovin_exo_icon_pause">aov</item>
    <item type="drawable" name="applovin_exo_icon_play">hn</item>
    <item type="drawable" name="applovin_exo_icon_previous">acv</item>
    <item type="drawable" name="applovin_exo_icon_repeat_all">aqf</item>
    <item type="drawable" name="applovin_exo_icon_repeat_off">be</item>
    <item type="drawable" name="applovin_exo_icon_repeat_one">pz</item>
    <item type="drawable" name="applovin_exo_icon_rewind">apm</item>
    <item type="drawable" name="applovin_exo_icon_shuffle_off">ase</item>
    <item type="drawable" name="applovin_exo_icon_shuffle_on">apv</item>
    <item type="drawable" name="applovin_exo_icon_stop">xv</item>
    <item type="drawable" name="applovin_ic_baseline_add_circle_outline">abp</item>
    <item type="drawable" name="applovin_ic_check_mark_bordered">alb</item>
    <item type="drawable" name="applovin_ic_check_mark_borderless">zc</item>
    <item type="drawable" name="applovin_ic_disclosure_arrow">uw</item>
    <item type="drawable" name="applovin_ic_mediation_adcolony">asw</item>
    <item type="drawable" name="applovin_ic_mediation_admob">is</item>
    <item type="drawable" name="applovin_ic_mediation_amazon_marketplace">akx</item>
    <item type="drawable" name="applovin_ic_mediation_applovin">sc</item>
    <item type="drawable" name="applovin_ic_mediation_bidmachine">ace</item>
    <item type="drawable" name="applovin_ic_mediation_chartboost">hk</item>
    <item type="drawable" name="applovin_ic_mediation_criteo">oq</item>
    <item type="drawable" name="applovin_ic_mediation_facebook">mq</item>
    <item type="drawable" name="applovin_ic_mediation_fyber">ec</item>
    <item type="drawable" name="applovin_ic_mediation_google_ad_manager">cd</item>
    <item type="drawable" name="applovin_ic_mediation_hyprmx">nv</item>
    <item type="drawable" name="applovin_ic_mediation_inmobi">wm</item>
    <item type="drawable" name="applovin_ic_mediation_ironsource">um</item>
    <item type="drawable" name="applovin_ic_mediation_line">dk</item>
    <item type="drawable" name="applovin_ic_mediation_maio">hj</item>
    <item type="drawable" name="applovin_ic_mediation_mintegral">ej</item>
    <item type="drawable" name="applovin_ic_mediation_mobilefuse">aak</item>
    <item type="drawable" name="applovin_ic_mediation_mytarget">tu</item>
    <item type="drawable" name="applovin_ic_mediation_nend">fo</item>
    <item type="drawable" name="applovin_ic_mediation_ogury_presage">aks</item>
    <item type="drawable" name="applovin_ic_mediation_pangle">ani</item>
    <item type="drawable" name="applovin_ic_mediation_smaato">im</item>
    <item type="drawable" name="applovin_ic_mediation_tapjoy">ako</item>
    <item type="drawable" name="applovin_ic_mediation_tiktok">abj</item>
    <item type="drawable" name="applovin_ic_mediation_unity">aau</item>
    <item type="drawable" name="applovin_ic_mediation_verve">sv</item>
    <item type="drawable" name="applovin_ic_mediation_vungle">amy</item>
    <item type="drawable" name="applovin_ic_mediation_yandex">atm</item>
    <item type="drawable" name="applovin_ic_pause_icon">zb</item>
    <item type="drawable" name="applovin_ic_play_icon">av</item>
    <item type="drawable" name="applovin_ic_replay_icon">op</item>
    <item type="drawable" name="applovin_ic_warning">ef</item>
    <item type="drawable" name="applovin_ic_warning_outline">eu</item>
    <item type="drawable" name="applovin_ic_x_mark">si</item>
    <item type="drawable" name="fingerprint_dialog_error">alg</item>
    <item type="drawable" name="ic_arrow_back_icon">lu</item>
    <item type="drawable" name="ic_check_white_24dp">ajs</item>
    <item type="drawable" name="ic_clear_white_24dp">ij</item>
    <item type="drawable" name="ic_error_outline_white_24dp">ay</item>
    <item type="drawable" name="ic_header_back">afe</item>
    <item type="drawable" name="ic_header_paypal_mark_color">nd</item>
    <item type="drawable" name="ic_info_outline_white_24dp">agg</item>
    <item type="drawable" name="ic_paypal_color">adt</item>
    <item type="drawable" name="ic_paypal_mark_color">nf</item>
    <item type="drawable" name="toast_frame">rs</item>
    <item type="drawable" name="abc_ab_share_pack_mtrl_alpha">gr</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_000">zs</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_015">ab</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_000">ir</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_015">aey</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00001">qj</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00012">arr</item>
    <item type="drawable" name="abc_cab_background_top_mtrl_alpha">acb</item>
    <item type="drawable" name="abc_ic_commit_search_api_mtrl_alpha">acm</item>
    <item type="drawable" name="abc_list_divider_mtrl_alpha">df</item>
    <item type="drawable" name="abc_list_focused_holo">kh</item>
    <item type="drawable" name="abc_list_longpressed_holo">afq</item>
    <item type="drawable" name="abc_list_pressed_holo_dark">pl</item>
    <item type="drawable" name="abc_list_pressed_holo_light">arm</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_dark">os</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_light">yl</item>
    <item type="drawable" name="abc_menu_hardkey_panel_mtrl_mult">apu</item>
    <item type="drawable" name="abc_popup_background_mtrl_mult">aeg</item>
    <item type="drawable" name="abc_scrubber_control_off_mtrl_alpha">aro</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000">aqx</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005">akr</item>
    <item type="drawable" name="abc_scrubber_primary_mtrl_alpha">nu</item>
    <item type="drawable" name="abc_scrubber_track_mtrl_alpha">wv</item>
    <item type="drawable" name="abc_spinner_mtrl_am_alpha">aow</item>
    <item type="drawable" name="abc_switch_track_mtrl_alpha">aic</item>
    <item type="drawable" name="abc_tab_indicator_mtrl_alpha">aqj</item>
    <item type="drawable" name="abc_text_select_handle_left_mtrl">afn</item>
    <item type="drawable" name="abc_text_select_handle_middle_mtrl">alf</item>
    <item type="drawable" name="abc_text_select_handle_right_mtrl">aqh</item>
    <item type="drawable" name="abc_textfield_activated_mtrl_alpha">wr</item>
    <item type="drawable" name="abc_textfield_default_mtrl_alpha">vd</item>
    <item type="drawable" name="abc_textfield_search_activated_mtrl_alpha">aho</item>
    <item type="drawable" name="abc_textfield_search_default_mtrl_alpha">qu</item>
    <item type="drawable" name="amp_banner">aqe</item>
    <item type="drawable" name="amp_cancel">tv</item>
    <item type="drawable" name="amp_logo">abq</item>
    <item type="drawable" name="com_facebook_button_like_icon_selected">asv</item>
    <item type="drawable" name="com_facebook_close">rz</item>
    <item type="drawable" name="com_facebook_tooltip_black_background">lt</item>
    <item type="drawable" name="com_facebook_tooltip_black_bottomnub">aiz</item>
    <item type="drawable" name="com_facebook_tooltip_black_topnub">nj</item>
    <item type="drawable" name="com_facebook_tooltip_black_xout">ls</item>
    <item type="drawable" name="com_facebook_tooltip_blue_background">aen</item>
    <item type="drawable" name="com_facebook_tooltip_blue_bottomnub">mv</item>
    <item type="drawable" name="com_facebook_tooltip_blue_topnub">aec</item>
    <item type="drawable" name="com_facebook_tooltip_blue_xout">alx</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark_normal_background">ajt</item>
    <item type="drawable" name="common_google_signin_btn_icon_light_normal_background">qy</item>
    <item type="drawable" name="common_google_signin_btn_text_dark_normal_background">vt</item>
    <item type="drawable" name="common_google_signin_btn_text_light_normal_background">agi</item>
    <item type="drawable" name="googleg_disabled_color_18">abr</item>
    <item type="drawable" name="googleg_standard_color_18">cb</item>
    <item type="drawable" name="notification_bg_low_normal">adq</item>
    <item type="drawable" name="notification_bg_low_pressed">app</item>
    <item type="drawable" name="notification_bg_normal">gf</item>
    <item type="drawable" name="notification_bg_normal_pressed">gw</item>
    <item type="drawable" name="notify_panel_notification_icon_bg">rv</item>
    <item type="drawable" name="common_full_open_on_phone">yb</item>
</resources>
