<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="drawable" name="fingerprint_dialog_error">xp</item>
    <item type="drawable" name="ic_arrow_back_icon">el</item>
    <item type="drawable" name="ic_check_white_24dp">bv</item>
    <item type="drawable" name="ic_clear_white_24dp">afz</item>
    <item type="drawable" name="ic_error_outline_white_24dp">ja</item>
    <item type="drawable" name="ic_header_back">cr</item>
    <item type="drawable" name="ic_header_paypal_mark_color">i</item>
    <item type="drawable" name="ic_info_outline_white_24dp">aob</item>
    <item type="drawable" name="ic_paypal_color">dy</item>
    <item type="drawable" name="ic_paypal_mark_color">ahy</item>
    <item type="drawable" name="toast_frame">vu</item>
    <item type="drawable" name="abc_ab_share_pack_mtrl_alpha">adc</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_000">le</item>
    <item type="drawable" name="abc_btn_check_to_on_mtrl_015">ha</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_000">hh</item>
    <item type="drawable" name="abc_btn_radio_to_on_mtrl_015">xl</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00001">ca</item>
    <item type="drawable" name="abc_btn_switch_to_on_mtrl_00012">sw</item>
    <item type="drawable" name="abc_cab_background_top_mtrl_alpha">afg</item>
    <item type="drawable" name="abc_ic_commit_search_api_mtrl_alpha">arn</item>
    <item type="drawable" name="abc_list_divider_mtrl_alpha">ev</item>
    <item type="drawable" name="abc_list_focused_holo">dw</item>
    <item type="drawable" name="abc_list_longpressed_holo">asq</item>
    <item type="drawable" name="abc_list_pressed_holo_dark">afm</item>
    <item type="drawable" name="abc_list_pressed_holo_light">lw</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_dark">xc</item>
    <item type="drawable" name="abc_list_selector_disabled_holo_light">ro</item>
    <item type="drawable" name="abc_menu_hardkey_panel_mtrl_mult">nq</item>
    <item type="drawable" name="abc_popup_background_mtrl_mult">kp</item>
    <item type="drawable" name="abc_scrubber_control_off_mtrl_alpha">aqz</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000">ajb</item>
    <item type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005">wl</item>
    <item type="drawable" name="abc_scrubber_primary_mtrl_alpha">aeb</item>
    <item type="drawable" name="abc_scrubber_track_mtrl_alpha">ac</item>
    <item type="drawable" name="abc_spinner_mtrl_am_alpha">jn</item>
    <item type="drawable" name="abc_switch_track_mtrl_alpha">qe</item>
    <item type="drawable" name="abc_tab_indicator_mtrl_alpha">fp</item>
    <item type="drawable" name="abc_text_select_handle_left_mtrl">yk</item>
    <item type="drawable" name="abc_text_select_handle_middle_mtrl">abl</item>
    <item type="drawable" name="abc_text_select_handle_right_mtrl">rw</item>
    <item type="drawable" name="abc_textfield_activated_mtrl_alpha">afv</item>
    <item type="drawable" name="abc_textfield_default_mtrl_alpha">hf</item>
    <item type="drawable" name="abc_textfield_search_activated_mtrl_alpha">aku</item>
    <item type="drawable" name="abc_textfield_search_default_mtrl_alpha">act</item>
    <item type="drawable" name="amp_banner">aa</item>
    <item type="drawable" name="amp_cancel">v</item>
    <item type="drawable" name="amp_logo">jf</item>
    <item type="drawable" name="com_facebook_button_like_icon_selected">xq</item>
    <item type="drawable" name="com_facebook_close">afp</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark_normal_background">abo</item>
    <item type="drawable" name="common_google_signin_btn_icon_light_normal_background">aie</item>
    <item type="drawable" name="common_google_signin_btn_text_dark_normal_background">di</item>
    <item type="drawable" name="common_google_signin_btn_text_light_normal_background">zj</item>
    <item type="drawable" name="googleg_disabled_color_18">aop</item>
    <item type="drawable" name="googleg_standard_color_18">any</item>
</resources>
